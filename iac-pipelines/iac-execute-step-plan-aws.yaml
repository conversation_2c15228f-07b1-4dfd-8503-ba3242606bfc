parameters:
  # Common
  - name: environment
  - name: taskcondition
    default: succeeded()

  # AWS Auth
  - name: awsServiceConnectionName
  - name: awsRegion

  # Terraform configuration
  - name: terraformProjectLocation
  - name: terraformGlobalTFVars
  - name: terraformLogLevel
    default: 'INFO'

  # Terraform CLI options
  - name: terraformCLIGlobalOptionsForPlan
  - name: terraformCLIOptionsForPlan

  # Plan options
  - name: planFilePath
    default: 'tfplan.out'
  - name: artifactname
    default: 'tfplan'

  # Not exposed options
  - name: terraformDestroyPlan
    type: boolean
    default: false
  - name: tfvarsToUseFromPipelineVariables
    type: object
    default: {}

### Plan steps
steps:

  # Terraform plan
  - task: AWSShellScript@1
    ${{ if ne(parameters.artifactname, 'tfplandestroy') }}: 
      displayName: Terraform plan
    ${{ else }}:
      displayName: Generate destroy plan
    name: plan
    condition: ${{ parameters.taskcondition }}
    env:
      TF_DESTROY_PLAN: ${{ lower(parameters.terraformDestroyPlan) }}
      ${{ each var in parameters.tfvarsToUseFromPipelineVariables }}:
        TF_VAR_${{ var.tfvarName }}: ${{ var.varName }}
    inputs:
      awsCredentials: ${{ parameters.awsServiceConnectionName }}
      regionName: ${{ parameters.awsRegion }}
      scriptType: inline
      workingDirectory: "$(Pipeline.Workspace)/source_repo/${{ parameters.terraformProjectLocation }}"
      inlineScript: |
        set -eu  # fail on error
        export TF_LOG=${{ parameters.terraformLogLevel}}
        TFVARS_PARAMS=""
        if [ -z "${{ parameters.terraformGlobalTFVars }}" ]; then
          GLOBAL_TFVARS="$(Pipeline.Workspace)/tooling_repo/env/${{ parameters.environment }}.tfvars"
        else
          GLOBAL_TFVARS="$(Pipeline.Workspace)/tooling_repo/env/${{ parameters.terraformGlobalTFVars }}.tfvars"
        fi
        echo "Using GLOBAL_TFVARS: $GLOBAL_TFVARS"
        LOCAL_TFVARS="$(Pipeline.Workspace)/source_repo/${{ parameters.terraformProjectLocation }}/${{ parameters.environment }}.tfvars"

        TF_EXTRA_ARGS=""
        if [[ -v TF_DESTROY_PLAN ]]; then
          if [[ "$TF_DESTROY_PLAN" == "true" ]]; then
            echo "##vso[task.logissue type=warning]The Terraform plan will be created with the '-destroy' option. This means that the plan will be a destruction plan for this entire Terraform-managed infrastructure!"
            TF_EXTRA_ARGS="-destroy"
          fi
        fi

        echo "*******************************************************"
        echo "Terraform inputs from tfvars files:"
        if [[ -f "$GLOBAL_TFVARS" ]]; then
          TFVARS_PARAMS="-var-file=$GLOBAL_TFVARS"
          echo 'GLOBAL_TFVARS:'
          cat $GLOBAL_TFVARS; echo
        else
          echo "##vso[task.logissue type=warning]The tfvars file at path $GLOBAL_TFVARS does not exist."
        fi
        if [[ -f "$LOCAL_TFVARS" ]]; then
          TFVARS_PARAMS="$TFVARS_PARAMS -var-file=$LOCAL_TFVARS"
          echo 'LOCAL_TFVARS:'
          cat $LOCAL_TFVARS; echo
        fi
        echo "*******************************************************"
        echo "Terraform inputs from pipeline variables:"
        printenv | grep "TF_VAR_" || true
        echo "*******************************************************"

        set +e
        terraform ${{ parameters.terraformCLIGlobalOptionsForPlan }} plan \
          -detailed-exitcode \
          $TFVARS_PARAMS \
          -out="$(Build.SourcesDirectory)/${{ parameters.planFilePath }}" $TF_EXTRA_ARGS ${{ parameters.terraformCLIOptionsForPlan }}
        retVal=$?
        set -e
        if [ $retVal -eq 2 ]; then
            echo '##vso[task.setvariable variable=terraform_changes_to_apply;isOutput=true]true'
            terraform ${{ parameters.terraformCLIGlobalOptionsForPlan }} show -json "$(Build.SourcesDirectory)/${{ parameters.planFilePath }}" | jq '.' > $(Build.SourcesDirectory)/tfplan.json
            terraform ${{ parameters.terraformCLIGlobalOptionsForPlan }} show -no-color "$(Build.SourcesDirectory)/${{ parameters.planFilePath }}" > $(Build.SourcesDirectory)/tfplan.txt
            echo '##vso[task.uploadsummary]$(Build.SourcesDirectory)/tfplan.txt'
            echo "------------------------------------------------------------"
            exit 0
        else
            if [ $retVal -eq 0 ]; then
              echo "##vso[task.logissue type=warning]Terraform plan detected that no changes are needed. Your current infrastructure matches the configuration, or nothing to destroy."
            fi
            echo '##vso[task.setvariable variable=terraform_changes_to_apply;isOutput=true]false'
        fi
        exit $retVal

  # Copy and Publish plan artifacts for deployment
  - ${{ if ne(parameters.planFilePath, '') }}:
    - task: CopyFiles@2
      displayName: Copy Terraform plan files to ArtifactStagingDirectory
      condition: ${{ parameters.taskcondition }}
      inputs:
        Contents: |
          $(Build.SourcesDirectory)/tfplan.json
          $(Build.SourcesDirectory)/${{ parameters.planFilePath }}
          $(Build.SourcesDirectory)/tfplan.txt
        TargetFolder: $(Build.ArtifactStagingDirectory)

    - task: PublishPipelineArtifact@1
      displayName: "Publish Terraform plan as pipeline artifact"
      condition: ${{ parameters.taskcondition }}
      inputs:
        targetPath: $(Build.ArtifactStagingDirectory)
        artifact: ${{ parameters.artifactname }}
