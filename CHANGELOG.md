## Tooling

### v5.0.0 - Feb 18, 2025 [current]
FEATURES:
- Added Google Cloud Platform key file based authentication
- Added environment files for GCP iactest projects

ENHANCEMENTS:
- Updated test option with a 3-way condition to run when plan comes back with no changes

### v4.2.0 - Nov 21, 2024
ENHANCEMENTS:
- Updated semantic pipeline as hcl files are no longer required

### v4.1.0 - Nov 19, 2024
FEATURES:
- Unlock state file in case cleanup step is required

### v4.0.0 - Nov 14, 2024
FEATURES:
- Enhanced execute pipeline to perform deployment and destroy in a single test action

### v3.0.0 - Oct 10, 2024
FEATURES:
- Pipelines and environment files are updated to use federated authentication

### v2.5.5 - Sep 12, 2024 
ENHANCEMENTS:
- NO_PROXY updated to remove *.core.windows.net and vault.azure.net if specified

### v2.5.2 - Jul 18, 2024
ENHANCEMENTS:
- Moved release page pipeline to AKS agentpool

### v2.5.1 - Jul 1, 2024
BUGFIX:
- Corrected Changelog

### v2.5.0 - Jun 25, 2024
ENHANCEMENTS:
- Updated prototypes

### v2.4.25-v2.4.26 - Jun 20, 2024
ENHANC<PERSON>ENTS:
- Added demo01 to environments

### v2.4.13-v2.4.24 - May 15, 2024
ENHANCEMENTS:
- Updated terraformrc

### v2.4.11-v2.4.12 - May 14, 2024
ENHANCEMENTS:
- Updated Terraform version

### v2.4.10 - May 7, 2024
ENHANCEMENTS:
- Improved Semantic pipeline

### v2.4.9 - May 3, 2024
ENHANCEMENTS:
- Improved Semantic pipeline

### v2.4.8 - April 29, 2024
ENHANCEMENTS:
- Improved Semantic pipeline

### v2.4.7 - April 19, 2024
ENHANCEMENTS:
- Remove the USAGE.md check

### v2.4.6 - April 19, 2024
ENHANCEMENTS:
- Remove the USAGE.md check for single module repoes from the semantic pipeline

### v2.4.5 - April 19, 2024
FEATURES:
- Moved subnet tools from network to tooling